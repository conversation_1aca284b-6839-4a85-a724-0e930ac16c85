import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  AlertCircle,
  Timer,
  Truck,
  CheckCircle2,
  MapPin,
  Phone,
  MessageCircle,
  Restaurant,
  CreditCard,
  Package,
  Clock,
  User,
  CheckCircle,
  Car,
  Navigation,
  DollarSign,
  Sparkles,
  Crown,
  Flame,
  Zap,
  Award,
  Target
} from 'lucide-react';
import { useOrdersStore } from '../../../stores/ordersStore';

// Enhanced Glass Card Component with Extreme Effects
const GlassCard: React.FC<{ children: React.ReactNode; className?: string; gradient?: string }> = ({ 
  children, 
  className = '', 
  gradient = 'from-white/10 to-white/5' 
}) => (
  <motion.div
    className={`backdrop-blur-xl bg-gradient-to-br ${gradient} border border-white/20 rounded-3xl shadow-2xl ${className}`}
    style={{
      boxShadow: '0 32px 64px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />
    
    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
    
    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

const SupplierOrderDetails: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { orders, updateOrderStatus } = useOrdersStore();
  const [isUpdating, setIsUpdating] = useState(false);

  const order = orders.find((o) => o.id === orderId);

  if (!order) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Same as supplier home */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />
          <ParticleSystem />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        <div className="relative min-h-screen flex items-center justify-center p-8" style={{ zIndex: 1 }}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15 }}
          >
            <GlassCard className="p-12 text-center max-w-md">
              <div className="bg-white/10 rounded-2xl p-6 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <Package size={40} className="text-gray-400" />
              </div>
              <h3 className="text-white text-2xl font-bold mb-4">Order Not Found</h3>
              <p className="text-white/80 mb-8">
                The order you're looking for doesn't exist or has been removed.
              </p>
              <button
                onClick={() => navigate(-1)}
                className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-8 py-3 rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 flex items-center gap-3 mx-auto"
              >
                <ArrowLeft size={20} />
                Go Back
              </button>
            </GlassCard>
          </motion.div>
        </div>
      </div>
    );
  }

  const handleMarkDelivered = async () => {
    setIsUpdating(true);
    try {
      updateOrderStatus(order.id, 'Delivered');
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate(-1);
    } catch (error) {
      console.error('Error updating order to Delivered:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleMarkPreparing = async () => {
    setIsUpdating(true);
    try {
      updateOrderStatus(order.id, 'Preparing');
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate(-1);
    } catch (error) {
      console.error('Error updating order to Preparing:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleMarkOnTheWay = async () => {
    setIsUpdating(true);
    try {
      updateOrderStatus(order.id, 'On the Way');
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate(-1);
    } catch (error) {
      console.error('Error updating order to On the Way:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Pending':
        return {
          color: '#ef4444',
          bgGradient: 'from-red-500 to-red-600',
          icon: AlertCircle,
          label: 'New Order'
        };
      case 'Preparing':
        return {
          color: '#f59e0b',
          bgGradient: 'from-amber-500 to-amber-600',
          icon: Timer,
          label: 'Preparing'
        };
      case 'On the Way':
        return {
          color: '#f97316',
          bgGradient: 'from-orange-500 to-orange-600',
          icon: Truck,
          label: 'On the Way'
        };
      case 'Delivered':
        return {
          color: '#10b981',
          bgGradient: 'from-emerald-500 to-emerald-600',
          icon: CheckCircle2,
          label: 'Delivered'
        };
      default:
        return {
          color: '#6b7280',
          bgGradient: 'from-gray-500 to-gray-600',
          icon: Package,
          label: status
        };
    }
  };

  const statusConfig = getStatusConfig(order.status);
  const StatusIcon = statusConfig.icon;

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background - Same as supplier home */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
        <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
        <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
        <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
        <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
        <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
        <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />
        <ParticleSystem />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
      </div>

      {/* Main Content Container */}
      <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Back Button */}
          <motion.button
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            onClick={() => navigate(-1)}
            className="flex items-center gap-3 text-white/80 hover:text-white transition-colors duration-300 mb-6"
          >
            <ArrowLeft size={24} />
            <span className="text-lg font-medium">Back to Orders</span>
          </motion.button>

          {/* EXTREME Header */}
          <motion.div
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          >
            <GlassCard
              className="overflow-hidden border-0 shadow-2xl"
              gradient={`${statusConfig.bgGradient} to-transparent`}
            >
              <div className={`bg-gradient-to-br ${statusConfig.bgGradient} p-12 relative`}>
                {/* Decorative Background Elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20" />
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/8 rounded-full translate-y-16 -translate-x-16" />

                <div className="relative z-10 space-y-6">
                  {/* Order Header */}
                  <div className="flex items-center gap-6">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                      className="relative"
                    >
                      <div className="bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-3xl p-6">
                        <StatusIcon size={48} className="text-white" />
                      </div>
                    </motion.div>

                    <div className="flex-1 space-y-3">
                      <motion.h1
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6, duration: 0.6 }}
                        className="text-white text-5xl font-black"
                      >
                        Order #{order.id}
                      </motion.h1>

                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7, duration: 0.6 }}
                        className="flex items-center gap-4"
                      >
                        <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                          <span className="text-white text-sm font-bold">
                            {statusConfig.label.toUpperCase()}
                          </span>
                        </div>
                        <span className="text-white/90 text-lg font-medium">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </span>
                      </motion.div>
                    </div>
                  </div>

                  {/* Order Stats */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8, duration: 0.6 }}
                    className="grid grid-cols-3 gap-6"
                  >
                    <div className="text-center space-y-2">
                      <p className="text-white/80 text-sm font-semibold">TOTAL</p>
                      <p className="text-white text-3xl font-black">₪{order.total.toFixed(0)}</p>
                    </div>

                    <div className="w-px bg-white/30"></div>

                    <div className="text-center space-y-2">
                      <p className="text-white/80 text-sm font-semibold">ITEMS</p>
                      <p className="text-white text-3xl font-black">
                        {order.items.reduce((sum, item) => sum + item.qty, 0)}
                      </p>
                    </div>

                    <div className="w-px bg-white/30"></div>

                    <div className="text-center space-y-2">
                      <p className="text-white/80 text-sm font-semibold">PAYMENT</p>
                      <p className="text-white text-3xl font-black">
                        {order.paymentMethod.toUpperCase()}
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>
            </GlassCard>
          </motion.div>

          {/* Enhanced Customer Info */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <GlassCard className="p-8">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4">
                    <User size={28} className="text-white" />
                  </div>
                  <h3 className="text-white text-2xl font-bold">Customer Details</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="bg-white/10 rounded-lg p-3">
                      <MapPin size={20} className="text-gray-300" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-400 text-sm font-semibold">ADDRESS</p>
                      <p className="text-white text-lg font-medium">
                        {order.address || 'No address provided'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="bg-white/10 rounded-lg p-3">
                      <Phone size={20} className="text-gray-300" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-400 text-sm font-semibold">PHONE</p>
                      <p className="text-white text-lg font-medium">
                        {order.phone}
                      </p>
                    </div>
                  </div>

                  {order.notes && (
                    <div className="flex items-start gap-4">
                      <div className="bg-white/10 rounded-lg p-3">
                        <MessageCircle size={20} className="text-gray-300" />
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-400 text-sm font-semibold">NOTE</p>
                        <p className="text-white text-lg font-medium">
                          {order.notes}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </GlassCard>
          </motion.div>

          {/* Enhanced Order Items */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <GlassCard className="p-8">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl p-4">
                    <Restaurant size={28} className="text-white" />
                  </div>
                  <h3 className="text-white text-2xl font-bold">Order Items</h3>
                </div>

                <div className="space-y-4">
                  {order.items.map((item, idx) => (
                    <motion.div
                      key={idx}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 + idx * 0.1, duration: 0.4 }}
                    >
                      <GlassCard
                        className="p-6 bg-white/5 border-white/10"
                        gradient="from-white/5 to-white/2"
                      >
                        <div className="flex items-center gap-4">
                          <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-3 min-w-[60px] flex items-center justify-center">
                            <span className="text-white text-lg font-black">
                              {item.qty}×
                            </span>
                          </div>

                          <div className="flex-1 space-y-1">
                            <h4 className="text-white text-xl font-bold">
                              {item.product.name}
                            </h4>
                            <p className="text-gray-400 text-sm">
                              {item.product.category}
                            </p>
                          </div>

                          <div className="text-right space-y-1">
                            <p className="text-white text-xl font-black">
                              ₪{item.finalPrice.toFixed(2)}
                            </p>
                            <p className="text-gray-400 text-sm">
                              ₪{(item.finalPrice / item.qty).toFixed(2)} each
                            </p>
                          </div>
                        </div>
                      </GlassCard>
                    </motion.div>
                  ))}
                </div>
              </div>
            </GlassCard>
          </motion.div>

          {/* Enhanced Payment Summary */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <GlassCard className="p-8">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl p-4">
                    <CreditCard size={28} className="text-white" />
                  </div>
                  <h3 className="text-white text-2xl font-bold">Payment Summary</h3>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300 text-lg">Subtotal</span>
                    <span className="text-white text-xl font-semibold">
                      ₪{order.subtotal.toFixed(2)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-300 text-lg">Delivery Fee</span>
                    <span className="text-white text-xl font-semibold">
                      ₪{order.deliveryFee.toFixed(2)}
                    </span>
                  </div>

                  {order.promoDiscount > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-emerald-400 text-lg">Promo Discount</span>
                      <span className="text-emerald-400 text-xl font-semibold">
                        -₪{order.promoDiscount.toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="w-full h-px bg-white/20"></div>

                  <div className="flex justify-between items-center">
                    <span className="text-white text-2xl font-bold">Total</span>
                    <span className="text-white text-3xl font-black">
                      ₪{order.total.toFixed(2)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Payment Method</span>
                    <div className={`px-3 py-1 rounded-lg ${
                      order.paymentMethod === 'cash'
                        ? 'bg-amber-500'
                        : 'bg-blue-500'
                    }`}>
                      <span className="text-white text-sm font-semibold">
                        {order.paymentMethod.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </motion.div>

          {/* Enhanced Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
            className="space-y-4"
          >
            {order.status === 'Pending' && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleMarkPreparing}
                disabled={isUpdating}
                className="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white py-6 px-8 rounded-2xl font-bold text-xl transition-all duration-300 flex items-center justify-center gap-4 shadow-2xl disabled:opacity-50"
              >
                <CheckCircle size={28} />
                <span>{isUpdating ? 'Accepting...' : 'Accept & Start Preparing'}</span>
              </motion.button>
            )}

            {order.status === 'Preparing' && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleMarkOnTheWay}
                disabled={isUpdating}
                className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-6 px-8 rounded-2xl font-bold text-xl transition-all duration-300 flex items-center justify-center gap-4 shadow-2xl disabled:opacity-50"
              >
                <Car size={28} />
                <span>{isUpdating ? 'Updating...' : 'Mark On The Way'}</span>
              </motion.button>
            )}

            {order.status === 'On the Way' && (
              <div className="space-y-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => navigate(`/supplier/tracking/${order.id}`)}
                  className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white py-6 px-8 rounded-2xl font-bold text-xl transition-all duration-300 flex items-center justify-center gap-4 shadow-2xl"
                >
                  <Navigation size={28} />
                  <span>Track Delivery</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleMarkDelivered}
                  disabled={isUpdating}
                  className="w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white py-6 px-8 rounded-2xl font-bold text-xl transition-all duration-300 flex items-center justify-center gap-4 shadow-2xl disabled:opacity-50"
                >
                  <CheckCircle2 size={28} />
                  <span>{isUpdating ? 'Marking...' : 'Mark as Delivered'}</span>
                </motion.button>
              </div>
            )}

            {order.status === 'Delivered' && (
              <GlassCard
                className="p-8 bg-emerald-500/20 border-emerald-400/30"
                gradient="from-emerald-500/20 to-emerald-600/10"
              >
                <div className="flex items-center justify-center gap-4">
                  <CheckCircle2 size={40} className="text-emerald-400" />
                  <div className="text-center">
                    <h4 className="text-emerald-400 text-2xl font-black">
                      Order Completed! 🎉
                    </h4>
                    <p className="text-emerald-300 text-lg">
                      {order.paymentMethod === 'cash'
                        ? 'Payment received'
                        : 'Payment processed'
                      }
                    </p>
                  </div>
                </div>
              </GlassCard>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SupplierOrderDetails;
